
using SampleAPI.Logic;

var builder = WebApplication.CreateBuilder(args);

// 基本的なサービスを追加
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 自作のLogicを追加（これだけ！）
builder.Services.AddScoped<LogicInterface, LogicDef>();

var app = builder.Build();

// 開発環境でSwaggerを有効化
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// コントローラーのルーティングを有効化
app.MapControllers();

app.Run();
