using Microsoft.AspNetCore.Mvc;
using SampleAPI.Models;
using SampleAPI.Services;

namespace SampleAPI.Controllers
{
    /// <summary>
    /// プレゼンテーション層のコントローラー
    /// HTTPリクエスト/レスポンスの処理とアプリケーションサービスへの委譲を担当
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CalculationController : ControllerBase
    {
        private readonly ICalculationService _calculationService;

        public CalculationController(ICalculationService calculationService)
        {
            _calculationService = calculationService;
        }

        /// <summary>
        /// 計算を実行し、履歴として保存する
        /// </summary>
        [HttpPost("calculate")]
        public async Task<IActionResult> Calculate([FromBody] CalculationRequest request)
        {
            try
            {
                var result = await _calculationService.PerformCalculationAsync(
                    request.A, request.B, request.Operation);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// 簡単なSum計算（後方互換性のため）
        /// </summary>
        [HttpGet("sum")]
        public async Task<IActionResult> GetSum(int a, int b)
        {
            try
            {
                var result = await _calculationService.PerformCalculationAsync(a, b, "sum");
                return Ok(new { a, b, result = result.Result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 計算履歴を取得する
        /// </summary>
        [HttpGet("history")]
        public async Task<IActionResult> GetHistory()
        {
            try
            {
                var history = await _calculationService.GetCalculationHistoryAsync();
                return Ok(history);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }
    }
}
