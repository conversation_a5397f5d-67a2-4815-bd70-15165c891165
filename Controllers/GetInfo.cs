using Microsoft.AspNetCore.Mvc;
using SampleAPI.Logic;

namespace SampleAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CalculationController : ControllerBase
    {
        private readonly LogicInterface _logic;

        public CalculationController(LogicInterface logic)
        {
            _logic = logic;
        }

        [HttpGet("sum")]
        public IActionResult GetSum(int a, int b)
        {
            try
            {
                var result = _logic.sum(a, b);
                return Ok(new { a, b, result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
