using System;

namespace SampleAPI.Logic
{
    /// <summary>
    /// 複雑な処理を含むLogicクラス
    /// データベースアクセスや外部API呼び出しを含む
    /// </summary>
    public class ComplexLogic : LogicInterface
    {
        private readonly string _connectionString;
        private readonly HttpClient _httpClient;

        public ComplexLogic(string connectionString, HttpClient httpClient)
        {
            _connectionString = connectionString;
            _httpClient = httpClient;
        }

        public int sum(int a, int b)
        {
            // 1. データベースに計算履歴を保存
            SaveToDatabase(a, b, "sum");
            
            // 2. 外部APIで検証
            ValidateWithExternalAPI(a, b);
            
            // 3. 実際の計算
            var result = a + b;
            
            // 4. 結果をログに記録
            LogResult(a, b, result, "sum");
            
            return result;
        }

        public int subtract(int a, int b)
        {
            SaveToDatabase(a, b, "subtract");
            ValidateWithExternalAPI(a, b);
            var result = a - b;
            LogResult(a, b, result, "subtract");
            return result;
        }

        public int multiply(int a, int b)
        {
            SaveToDatabase(a, b, "multiply");
            ValidateWithExternalAPI(a, b);
            var result = a * b;
            LogResult(a, b, result, "multiply");
            return result;
        }

        private void SaveToDatabase(int a, int b, string operation)
        {
            // データベースアクセス（時間がかかる）
            Console.WriteLine($"データベースに保存: {a} {operation} {b}");
            Thread.Sleep(100); // データベースアクセスをシミュレート
        }

        private void ValidateWithExternalAPI(int a, int b)
        {
            // 外部API呼び出し（時間がかかる、失敗する可能性がある）
            Console.WriteLine($"外部API検証: {a}, {b}");
            Thread.Sleep(50); // API呼び出しをシミュレート
        }

        private void LogResult(int a, int b, int result, string operation)
        {
            Console.WriteLine($"計算完了: {a} {operation} {b} = {result}");
        }
    }
}
