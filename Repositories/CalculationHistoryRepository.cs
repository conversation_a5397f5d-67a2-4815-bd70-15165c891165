using Microsoft.EntityFrameworkCore;
using SampleAPI.DbContexts;
using SampleAPI.Entities;
using SampleAPI.Models;

namespace SampleAPI.Repositories
{
    /// <summary>
    /// 計算履歴のリポジトリ実装
    /// データベースとの直接的なやり取りを実装
    /// </summary>
    public class CalculationHistoryRepository : ICalculationHistoryRepository
    {
        private readonly DriverDbContext _context;

        public CalculationHistoryRepository(DriverDbContext context)
        {
            _context = context;
        }

        public async Task<int> SaveCalculationAsync(CalculationHistory calculation)
        {
            var entity = new CalculationHistoryEntity
            {
                InputA = calculation.InputA,
                InputB = calculation.InputB,
                Result = calculation.Result,
                Operation = calculation.Operation,
                CreatedAt = calculation.CreatedAt
            };

            _context.CalculationHistories.Add(entity);
            await _context.SaveChangesAsync();
            
            return entity.Id;
        }

        public async Task<IEnumerable<CalculationHistory>> GetAllCalculationsAsync()
        {
            var entities = await _context.CalculationHistories
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return entities.Select(MapToModel);
        }

        public async Task<CalculationHistory?> GetCalculationByIdAsync(int id)
        {
            var entity = await _context.CalculationHistories
                .FirstOrDefaultAsync(x => x.Id == id);

            return entity != null ? MapToModel(entity) : null;
        }

        public async Task<IEnumerable<CalculationHistory>> GetCalculationsByDateRangeAsync(DateTime from, DateTime to)
        {
            var entities = await _context.CalculationHistories
                .Where(x => x.CreatedAt >= from && x.CreatedAt <= to)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return entities.Select(MapToModel);
        }

        private static CalculationHistory MapToModel(CalculationHistoryEntity entity)
        {
            return new CalculationHistory
            {
                Id = entity.Id,
                InputA = entity.InputA,
                InputB = entity.InputB,
                Result = entity.Result,
                Operation = entity.Operation,
                CreatedAt = entity.CreatedAt
            };
        }
    }
}
