using SampleAPI.Models;

namespace SampleAPI.Repositories
{
    /// <summary>
    /// データアクセス層のインターフェース
    /// データベースとの直接的なやり取りを担当
    /// </summary>
    public interface ICalculationHistoryRepository
    {
        /// <summary>
        /// 計算履歴を保存する
        /// </summary>
        Task<int> SaveCalculationAsync(CalculationHistory calculation);
        
        /// <summary>
        /// 全ての計算履歴を取得する
        /// </summary>
        Task<IEnumerable<CalculationHistory>> GetAllCalculationsAsync();
        
        /// <summary>
        /// 指定されたIDの計算履歴を取得する
        /// </summary>
        Task<CalculationHistory?> GetCalculationByIdAsync(int id);
        
        /// <summary>
        /// 指定された期間の計算履歴を取得する
        /// </summary>
        Task<IEnumerable<CalculationHistory>> GetCalculationsByDateRangeAsync(DateTime from, DateTime to);
    }
}
