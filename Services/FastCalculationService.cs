using SampleAPI.Logic;
using SampleAPI.Models;
using SampleAPI.Repositories;

namespace SampleAPI.Services
{
    /// <summary>
    /// 高速版の計算サービス（履歴保存なし）
    /// 同じインターフェースを実装しているので、簡単に切り替え可能
    /// </summary>
    public class FastCalculationService : ICalculationService
    {
        private readonly LogicInterface _logic;

        public FastCalculationService(LogicInterface logic)
        {
            _logic = logic;
        }

        public async Task<CalculationResult> PerformCalculationAsync(int a, int b, string operation)
        {
            // 高速版：履歴保存をスキップ
            int result = operation.ToLower() switch
            {
                "sum" => _logic.sum(a, b),
                _ => throw new ArgumentException($"Unsupported operation: {operation}")
            };

            // 履歴保存なし、即座に結果を返す
            return new CalculationResult
            {
                A = a,
                B = b,
                Result = result,
                Operation = operation,
                CalculatedAt = DateTime.UtcNow,
                HistoryId = 0 // 履歴なし
            };
        }

        public async Task<IEnumerable<CalculationHistory>> GetCalculationHistoryAsync()
        {
            // 高速版：履歴機能なし
            return new List<CalculationHistory>();
        }
    }
}
