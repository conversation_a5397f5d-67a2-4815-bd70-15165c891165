using SampleAPI.Logic;
using SampleAPI.Models;
using SampleAPI.Repositories;

namespace SampleAPI.Services
{
    /// <summary>
    /// アプリケーションサービスの実装
    /// ビジネスロジックの組み合わせとワークフロー制御を担当
    /// </summary>
    public class CalculationService : ICalculationService
    {
        private readonly LogicInterface _logic;
        private readonly ICalculationHistoryRepository _repository;

        public CalculationService(LogicInterface logic, ICalculationHistoryRepository repository)
        {
            _logic = logic;
            _repository = repository;
        }

        public async Task<CalculationResult> PerformCalculationAsync(int a, int b, string operation)
        {
            // 1. ビジネスロジック層で計算実行
            int result = operation.ToLower() switch
            {
                "sum" => _logic.sum(a, b),
                _ => throw new ArgumentException($"Unsupported operation: {operation}")
            };

            // 2. 計算履歴をモデルに変換
            var history = new CalculationHistory
            {
                InputA = a,
                InputB = b,
                Result = result,
                Operation = operation,
                CreatedAt = DateTime.UtcNow
            };

            // 3. データアクセス層で履歴保存
            var historyId = await _repository.SaveCalculationAsync(history);

            // 4. レスポンスモデルを作成して返却
            return new CalculationResult
            {
                A = a,
                B = b,
                Result = result,
                Operation = operation,
                CalculatedAt = history.CreatedAt,
                HistoryId = historyId
            };
        }

        public async Task<IEnumerable<CalculationHistory>> GetCalculationHistoryAsync()
        {
            // データアクセス層から履歴を取得
            return await _repository.GetAllCalculationsAsync();
        }
    }
}
