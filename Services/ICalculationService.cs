using SampleAPI.Models;

namespace SampleAPI.Services
{
    /// <summary>
    /// アプリケーションサービス層のインターフェース
    /// ビジネスロジックの組み合わせとワークフロー制御を担当
    /// </summary>
    public interface ICalculationService
    {
        /// <summary>
        /// 計算を実行し、結果をログとして保存する
        /// </summary>
        Task<CalculationResult> PerformCalculationAsync(int a, int b, string operation);
        
        /// <summary>
        /// 計算履歴を取得する
        /// </summary>
        Task<IEnumerable<CalculationHistory>> GetCalculationHistoryAsync();
    }
}
