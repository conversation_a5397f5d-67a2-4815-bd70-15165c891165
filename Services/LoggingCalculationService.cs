using SampleAPI.Models;

namespace SampleAPI.Services
{
    /// <summary>
    /// ログ機能付きの計算サービス（デコレーターパターン）
    /// 既存の実装にログ機能を追加
    /// </summary>
    public class LoggingCalculationService : ICalculationService
    {
        private readonly ICalculationService _innerService;
        private readonly ILogger<LoggingCalculationService> _logger;

        public LoggingCalculationService(ICalculationService innerService, ILogger<LoggingCalculationService> logger)
        {
            _innerService = innerService;
            _logger = logger;
        }

        public async Task<CalculationResult> PerformCalculationAsync(int a, int b, string operation)
        {
            _logger.LogInformation($"計算開始: {a} {operation} {b}");
            
            try
            {
                var result = await _innerService.PerformCalculationAsync(a, b, operation);
                
                _logger.LogInformation($"計算完了: {a} {operation} {b} = {result.Result}");
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"計算エラー: {a} {operation} {b}");
                throw;
            }
        }

        public async Task<IEnumerable<CalculationHistory>> GetCalculationHistoryAsync()
        {
            _logger.LogInformation("履歴取得開始");
            
            var histories = await _innerService.GetCalculationHistoryAsync();
            
            _logger.LogInformation($"履歴取得完了: {histories.Count()}件");
            
            return histories;
        }
    }
}
