using SampleAPI.Models;

namespace SampleAPI.Services
{
    /// <summary>
    /// テスト用のモック実装
    /// データベースや外部サービスに依存せずにテストできる
    /// </summary>
    public class MockCalculationService : ICalculationService
    {
        public async Task<CalculationResult> PerformCalculationAsync(int a, int b, string operation)
        {
            // テスト用の固定値を返す
            return new CalculationResult
            {
                A = a,
                B = b,
                Result = a + b, // 簡単な計算
                Operation = operation,
                CalculatedAt = new DateTime(2024, 1, 1), // 固定日時
                HistoryId = 999 // テスト用ID
            };
        }

        public async Task<IEnumerable<CalculationHistory>> GetCalculationHistoryAsync()
        {
            // テスト用の固定データを返す
            return new List<CalculationHistory>
            {
                new CalculationHistory
                {
                    Id = 1,
                    InputA = 5,
                    InputB = 3,
                    Result = 8,
                    Operation = "sum",
                    CreatedAt = new DateTime(2024, 1, 1)
                }
            };
        }
    }
}
