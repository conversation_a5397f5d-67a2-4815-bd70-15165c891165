namespace SampleAPI.Models
{
    /// <summary>
    /// 計算結果のレスポンスモデル
    /// </summary>
    public class CalculationResult
    {
        public int A { get; set; }
        public int B { get; set; }
        public int Result { get; set; }
        public string Operation { get; set; } = string.Empty;
        public DateTime CalculatedAt { get; set; }
        public int HistoryId { get; set; }
    }

    /// <summary>
    /// 計算履歴のモデル
    /// </summary>
    public class CalculationHistory
    {
        public int Id { get; set; }
        public int InputA { get; set; }
        public int InputB { get; set; }
        public int Result { get; set; }
        public string Operation { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 計算リクエストのDTO
    /// </summary>
    public class CalculationRequest
    {
        public int A { get; set; }
        public int B { get; set; }
        public string Operation { get; set; } = "sum";
    }
}
