using System.ComponentModel.DataAnnotations;

namespace SampleAPI.Entities
{
    /// <summary>
    /// 計算履歴のエンティティ（データベーステーブルに対応）
    /// </summary>
    public class CalculationHistoryEntity
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int InputA { get; set; }
        
        [Required]
        public int InputB { get; set; }
        
        [Required]
        public int Result { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Operation { get; set; } = string.Empty;
        
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
